"""
K=5-----> 5th char in word until it reached more than or equal to 5 char.
word="a"---->each_char_change="b"---->word="ab"
word="ab"---->each_char_change="bc"---->word="abbc"
word="abbc"---->each_char_change="bccd"---->word="abbcbccd"
return k=5th element = b
"""
k=5
word="a"
i=len(word)
while i<=k:
    for j in word:
        unicode=ord(j)#gives int value for a single string
        word=word+chr(unicode+1)# chr gives the string if given int value unicode
    i=len(word) #to update the i value for "while loop" because idk how to do it.
print(word[k-1])

